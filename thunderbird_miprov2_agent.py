#!/usr/bin/env python3
"""
DSPy-based Log Anomaly Detection Agent for Thunderbird Dataset using MIPROv2 Optimization
Uses Ollama with qwen3:30b model and MIPROv2 optimization for better performance
"""

import dspy
import pandas as pd
import re
from typing import List, Tuple, Dict
import random
from datetime import datetime


class LogAnomalySignature(dspy.Signature):
    """Classify a log entry as normal or anomalous based on its content."""
    
    log_entry = dspy.InputField(desc="The log entry content to analyze")
    classification = dspy.OutputField(desc="Classification: 'normal' or 'anomalous'")
    reasoning = dspy.OutputField(desc="Brief explanation for the classification")


class LogAnomalyDetector(dspy.Module):
    """DSPy module for detecting anomalies in log entries."""
    
    def __init__(self):
        super().__init__()
        self.classify = dspy.ChainOfThought(LogAnomalySignature)
    
    def forward(self, log_entry: str):
        """Forward pass through the module."""
        result = self.classify(log_entry=log_entry)
        return result


class ThunderbirdDatasetProcessor:
    """Processes the Thunderbird dataset for training and evaluation."""
    
    def __init__(self, dataset_path: str):
        self.dataset_path = dataset_path
        self.data = []
    
    def parse_log_line(self, line: str) -> Dict:
        """Parse a single log line from Thunderbird format."""
        parts = line.strip().split(' ', 4)
        if len(parts) < 5:
            return None
        
        alert_category = parts[0]
        timestamp = parts[1]
        date = parts[2]
        node = parts[3]
        message = parts[4]
        
        # Label: "-" means normal (non-alert), anything else is anomalous (alert)
        is_anomalous = alert_category != "-"
        
        return {
            'alert_category': alert_category,
            'timestamp': timestamp,
            'date': date,
            'node': node,
            'message': message,
            'is_anomalous': is_anomalous,
            'label': 'anomalous' if is_anomalous else 'normal',
            'full_entry': line.strip()
        }
    
    def load_sample_data(self, num_samples: int = 1000, balance_ratio: float = 0.3) -> List[Dict]:
        """Load a balanced sample of the dataset."""
        print(f"Loading sample data from {self.dataset_path}...")
        
        normal_samples = []
        anomalous_samples = []
        
        with open(self.dataset_path, 'r', encoding='utf-8', errors='ignore') as f:
            for line_num, line in enumerate(f):
                if line_num % 10000 == 0:
                    print(f"Processed {line_num} lines...")
                
                parsed = self.parse_log_line(line)
                if parsed is None:
                    continue
                
                if parsed['is_anomalous']:
                    anomalous_samples.append(parsed)
                else:
                    normal_samples.append(parsed)
                
                # Stop when we have enough samples
                target_anomalous = int(num_samples * balance_ratio)
                target_normal = num_samples - target_anomalous
                
                if (len(anomalous_samples) >= target_anomalous and 
                    len(normal_samples) >= target_normal):
                    break
        
        # Balance the dataset
        selected_anomalous = random.sample(anomalous_samples, 
                                         min(target_anomalous, len(anomalous_samples)))
        selected_normal = random.sample(normal_samples, 
                                      min(target_normal, len(normal_samples)))
        
        all_samples = selected_anomalous + selected_normal
        random.shuffle(all_samples)
        
        print(f"Loaded {len(selected_normal)} normal and {len(selected_anomalous)} anomalous samples")
        return all_samples
    
    def create_dspy_examples(self, data: List[Dict], for_training: bool = True) -> List[dspy.Example]:
        """Convert parsed data to DSPy examples."""
        examples = []
        for item in data:
            # Create a clean log entry without the alert category for classification
            log_parts = item['full_entry'].split(' ', 1)
            if len(log_parts) > 1:
                clean_entry = log_parts[1]  # Remove the alert category
            else:
                clean_entry = item['message']
            
            if for_training:
                # For training: include the correct classification and reasoning
                example = dspy.Example(
                    log_entry=clean_entry,
                    classification=item['label'],
                    reasoning=f"This log entry is {item['label']} based on its content and patterns."
                ).with_inputs('log_entry')
            else:
                # For testing: only include the log entry, no ground truth labels
                example = dspy.Example(
                    log_entry=clean_entry
                ).with_inputs('log_entry')
                # Store the ground truth separately for evaluation
                example.ground_truth_classification = item['label']
            
            examples.append(example)
        
        return examples


def setup_ollama_client(base_url: str = "http://*************:11434"):
    """Setup Ollama client with qwen3:30b model."""
    print(f"Setting up Ollama client at {base_url}")
    lm = dspy.LM("ollama_chat/qwen3:30b", api_base="http://*************:11434", temperature=0.1)
    dspy.configure(lm=lm)
    return lm


def accuracy_metric(example, pred, trace=None):
    """Custom accuracy metric for MIPROv2."""
    predicted_class = pred.classification.lower().strip()
    actual_class = example.classification.lower().strip()
    return predicted_class == actual_class


def train_and_evaluate_miprov2():
    """Main training and evaluation function using MIPROv2."""
    print("Starting Thunderbird Log Anomaly Detection Training with MIPROv2...")
    
    # Setup Ollama
    setup_ollama_client()
    
    # Load and process data
    processor = ThunderbirdDatasetProcessor("dataset/Thunderbird/Thunderbird.log")
    sample_data = processor.load_sample_data(num_samples=500, balance_ratio=0.3)
    
    # Split data into train and test only
    train_size = int(0.8 * len(sample_data))
    
    train_data = sample_data[:train_size]
    test_data = sample_data[train_size:]
    
    # Create examples with appropriate visibility of labels
    train_examples = processor.create_dspy_examples(train_data, for_training=True)
    test_examples = processor.create_dspy_examples(test_data, for_training=False)  # Test should not see labels
    
    print(f"Dataset split: {len(train_examples)} train, {len(test_examples)} test")
    
    # Initialize the model
    detector = LogAnomalyDetector()
    
    # Setup MIPROv2 optimizer
    print("Setting up MIPROv2 optimizer...")
    optimizer = dspy.MIPROv2(
        metric=accuracy_metric,
        num_candidates=10,  # Number of candidate programs to generate
        init_temperature=1.0,  # Initial temperature for sampling
        verbose=True
    )
    
    print("Optimizing model with MIPROv2...")
    try:
        optimized_detector = optimizer.compile(
            detector,
            trainset=train_examples,
            num_trials=20,  # Number of optimization trials
            max_bootstrapped_demos=8,  # Maximum number of demonstrations
            max_labeled_demos=8,  # Maximum number of labeled demonstrations
        )
    except Exception as e:
        print(f"MIPROv2 optimization failed: {e}")
        print("Falling back to base model...")
        optimized_detector = detector
    
    # Evaluate on test set
    print("Evaluating on test set...")
    correct = 0
    total = 0
    
    for example in test_examples:
        try:
            # The agent only sees the log entry, not the ground truth classification
            prediction = optimized_detector(log_entry=example.log_entry)
            predicted_class = prediction.classification.lower().strip()
            actual_class = example.ground_truth_classification.lower().strip()
            
            if predicted_class == actual_class:
                correct += 1
            
            total += 1
            
            if total <= 5:  # Show first 5 predictions
                print(f"\nExample {total}:")
                print(f"Log: {example.log_entry[:100]}...")
                print(f"Actual: {actual_class}")
                print(f"Predicted: {predicted_class}")
                print(f"Reasoning: {prediction.reasoning}")
                print(f"Correct: {predicted_class == actual_class}")
                
        except Exception as e:
            print(f"Error processing example: {e}")
            continue
    
    accuracy = correct / total if total > 0 else 0
    print(f"\nMIPROv2 Optimized Results:")
    print(f"Accuracy: {accuracy:.3f} ({correct}/{total})")
    
    return optimized_detector


def compare_optimizers():
    """Compare MIPROv2 with LabeledFewShot on the same dataset."""
    print("=== Comparing MIPROv2 vs LabeledFewShot ===\n")
    
    # Setup Ollama
    setup_ollama_client()
    
    # Load same dataset for fair comparison
    processor = ThunderbirdDatasetProcessor("dataset/Thunderbird/Thunderbird.log")
    sample_data = processor.load_sample_data(num_samples=200, balance_ratio=0.3)
    
    train_size = int(0.8 * len(sample_data))
    train_data = sample_data[:train_size]
    test_data = sample_data[train_size:]
    
    train_examples = processor.create_dspy_examples(train_data, for_training=True)
    test_examples = processor.create_dspy_examples(test_data, for_training=False)
    
    print(f"Comparison dataset: {len(train_examples)} train, {len(test_examples)} test")
    
    # Test LabeledFewShot
    print("\n1. Testing LabeledFewShot...")
    detector1 = LogAnomalyDetector()
    optimizer1 = dspy.LabeledFewShot(k=8)
    optimized_detector1 = optimizer1.compile(detector1, trainset=train_examples)
    
    # Test MIPROv2
    print("\n2. Testing MIPROv2...")
    detector2 = LogAnomalyDetector()
    optimizer2 = dspy.MIPROv2(metric=accuracy_metric, num_candidates=5, verbose=False)
    
    try:
        optimized_detector2 = optimizer2.compile(
            detector2,
            trainset=train_examples,
            num_trials=10,
            max_bootstrapped_demos=4,
            max_labeled_demos=4,
        )
    except Exception as e:
        print(f"MIPROv2 failed: {e}, using base model")
        optimized_detector2 = detector2
    
    # Evaluate both
    results = {}
    for name, model in [("LabeledFewShot", optimized_detector1), ("MIPROv2", optimized_detector2)]:
        print(f"\nEvaluating {name}...")
        correct = 0
        total = 0
        
        for example in test_examples:
            try:
                prediction = model(log_entry=example.log_entry)
                predicted_class = prediction.classification.lower().strip()
                actual_class = example.ground_truth_classification.lower().strip()
                
                if predicted_class == actual_class:
                    correct += 1
                total += 1
            except:
                continue
        
        accuracy = correct / total if total > 0 else 0
        results[name] = accuracy
        print(f"{name} Accuracy: {accuracy:.3f} ({correct}/{total})")
    
    print(f"\n=== Final Comparison ===")
    for name, acc in results.items():
        print(f"{name}: {acc:.3f}")
    
    return results


if __name__ == "__main__":
    # Set random seed for reproducibility
    random.seed(42)
    
    # Choose what to run
    print("Choose mode:")
    print("1. Train with MIPROv2 only")
    print("2. Compare MIPROv2 vs LabeledFewShot")
    
    # For now, let's run the comparison
    compare_optimizers()
    
    print("\nThunderbird MIPROv2 training completed!")
