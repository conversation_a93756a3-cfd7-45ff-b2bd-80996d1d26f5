#!/usr/bin/env python3
"""
DSPy-based Log Anomaly Detection Agent for BGL Dataset
Uses Ollama with qwen:30b model and LabeledFewShot optimization
"""

import dspy
import pandas as pd
import re
from typing import List, Tuple, Dict
import random
from datetime import datetime


class LogAnomalySignature(dspy.Signature):
    """Classify a log entry as normal or anomalous based on its content."""
    
    log_entry = dspy.InputField(desc="The log entry content to analyze")
    classification = dspy.OutputField(desc="Classification: 'normal' or 'anomalous'")
    reasoning = dspy.OutputField(desc="Brief explanation for the classification")


class LogAnomalyDetector(dspy.Module):
    """DSPy module for detecting anomalies in log entries."""
    
    def __init__(self):
        super().__init__()
        self.classify = dspy.ChainOfThought(LogAnomalySignature)
    
    def forward(self, log_entry: str):
        """Forward pass through the module."""
        result = self.classify(log_entry=log_entry)
        return result


class BGLDatasetProcessor:
    """Processes the BGL dataset for training and evaluation."""
    
    def __init__(self, dataset_path: str):
        self.dataset_path = dataset_path
        self.data = []
    
    def parse_log_line(self, line: str) -> Dict:
        """Parse a single log line from BGL format."""
        parts = line.strip().split(' ', 6)
        if len(parts) < 7:
            return None
        
        alert_category = parts[0]
        timestamp = parts[1]
        date = parts[2]
        location = parts[3]
        detailed_timestamp = parts[4]
        node = parts[5]
        message = parts[6]
        
        # Label: "-" means normal (non-alert), anything else is anomalous (alert)
        is_anomalous = alert_category != "-"
        
        return {
            'alert_category': alert_category,
            'timestamp': timestamp,
            'date': date,
            'location': location,
            'detailed_timestamp': detailed_timestamp,
            'node': node,
            'message': message,
            'is_anomalous': is_anomalous,
            'label': 'anomalous' if is_anomalous else 'normal',
            'full_entry': line.strip()
        }
    
    def load_sample_data(self, num_samples: int = 1000, balance_ratio: float = 0.3) -> List[Dict]:
        """Load a balanced sample of the dataset."""
        print(f"Loading sample data from {self.dataset_path}...")
        
        normal_samples = []
        anomalous_samples = []
        
        with open(self.dataset_path, 'r', encoding='utf-8', errors='ignore') as f:
            for line_num, line in enumerate(f):
                if line_num % 10000 == 0:
                    print(f"Processed {line_num} lines...")
                
                parsed = self.parse_log_line(line)
                if parsed is None:
                    continue
                
                if parsed['is_anomalous']:
                    anomalous_samples.append(parsed)
                else:
                    normal_samples.append(parsed)
                
                # Stop when we have enough samples
                target_anomalous = int(num_samples * balance_ratio)
                target_normal = num_samples - target_anomalous
                
                if (len(anomalous_samples) >= target_anomalous and 
                    len(normal_samples) >= target_normal):
                    break
        
        # Balance the dataset
        selected_anomalous = random.sample(anomalous_samples, 
                                         min(target_anomalous, len(anomalous_samples)))
        selected_normal = random.sample(normal_samples, 
                                      min(target_normal, len(normal_samples)))
        
        all_samples = selected_anomalous + selected_normal
        random.shuffle(all_samples)
        
        print(f"Loaded {len(selected_normal)} normal and {len(selected_anomalous)} anomalous samples")
        return all_samples
    
    def create_dspy_examples(self, data: List[Dict]) -> List[dspy.Example]:
        """Convert parsed data to DSPy examples."""
        examples = []
        for item in data:
            # Create a clean log entry without the alert category for classification
            log_parts = item['full_entry'].split(' ', 1)
            if len(log_parts) > 1:
                clean_entry = log_parts[1]  # Remove the alert category
            else:
                clean_entry = item['message']
            
            example = dspy.Example(
                log_entry=clean_entry,
                classification=item['label'],
                reasoning=f"This log entry is {item['label']} based on its content and patterns."
            ).with_inputs('log_entry')
            
            examples.append(example)
        
        return examples


def setup_ollama_client(base_url: str = "http://192.168.0.105:11434"):
    """Setup Ollama client with qwen:30b model."""
    print(f"Setting up Ollama client at {base_url}")
    
    # Configure DSPy to use Ollama
    ollama_client = dspy.OllamaLocal(
        model="qwen:30b",
        base_url=base_url,
        max_tokens=1000,
        temperature=0.1
    )
    
    dspy.settings.configure(lm=ollama_client)
    return ollama_client


def train_and_evaluate():
    """Main training and evaluation function."""
    print("Starting Log Anomaly Detection Training...")
    
    # Setup Ollama
    setup_ollama_client()
    
    # Load and process data
    processor = BGLDatasetProcessor("dataset/BGL/BGL.log")
    sample_data = processor.load_sample_data(num_samples=200, balance_ratio=0.3)
    examples = processor.create_dspy_examples(sample_data)
    
    # Split data
    train_size = int(0.7 * len(examples))
    val_size = int(0.15 * len(examples))
    
    train_examples = examples[:train_size]
    val_examples = examples[train_size:train_size + val_size]
    test_examples = examples[train_size + val_size:]
    
    print(f"Dataset split: {len(train_examples)} train, {len(val_examples)} val, {len(test_examples)} test")
    
    # Initialize the model
    detector = LogAnomalyDetector()
    
    # Setup optimizer
    optimizer = dspy.LabeledFewShot(k=16)  # Use 16 examples for few-shot learning
    
    print("Optimizing model with LabeledFewShot...")
    optimized_detector = optimizer.compile(
        detector,
        trainset=train_examples,
        valset=val_examples
    )
    
    # Evaluate on test set
    print("Evaluating on test set...")
    correct = 0
    total = 0
    
    for example in test_examples:
        try:
            prediction = optimized_detector(log_entry=example.log_entry)
            predicted_class = prediction.classification.lower().strip()
            actual_class = example.classification.lower().strip()
            
            if predicted_class == actual_class:
                correct += 1
            
            total += 1
            
            if total <= 5:  # Show first 5 predictions
                print(f"\nExample {total}:")
                print(f"Log: {example.log_entry[:100]}...")
                print(f"Actual: {actual_class}")
                print(f"Predicted: {predicted_class}")
                print(f"Reasoning: {prediction.reasoning}")
                print(f"Correct: {predicted_class == actual_class}")
        
        except Exception as e:
            print(f"Error processing example: {e}")
            continue
    
    accuracy = correct / total if total > 0 else 0
    print(f"\nFinal Results:")
    print(f"Accuracy: {accuracy:.3f} ({correct}/{total})")
    
    return optimized_detector


if __name__ == "__main__":
    # Set random seed for reproducibility
    random.seed(42)
    
    # Train and evaluate the model
    trained_model = train_and_evaluate()
    
    print("\nTraining completed!")
